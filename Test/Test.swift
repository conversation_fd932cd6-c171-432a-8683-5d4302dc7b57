//
//  Test.swift
//  Test
//
//  Created by gondo on 16/09/2025.
//

import WidgetKit
import SwiftUI

struct Provider: AppIntentTimelineProvider {
    func placeholder(in context: Context) -> SimpleEntry {
        SimpleEntry(date: Date(), count: 0, configuration: ConfigurationAppIntent())
    }

    func snapshot(for configuration: ConfigurationAppIntent, in context: Context) async -> SimpleEntry {
        let currentCount = CounterManager.shared.getCurrentCount()
        print("🔄 Widget Snapshot: Loading count: \(currentCount)")
        return SimpleEntry(date: Date(), count: currentCount, configuration: configuration)
    }

    func timeline(for configuration: ConfigurationAppIntent, in context: Context) async -> Timeline<SimpleEntry> {
        let currentCount = CounterManager.shared.getCurrentCount()
        let currentDate = Date()
        let entry = SimpleEntry(date: currentDate, count: currentCount, configuration: configuration)

        print("📅 Widget Timeline: Creating entry with count: \(currentCount)")

        // Update more frequently for testing, then every 5 minutes
        let nextUpdate = Calendar.current.date(byAdding: .minute, value: 5, to: currentDate)!
        return Timeline(entries: [entry], policy: .after(nextUpdate))
    }
}

struct SimpleEntry: TimelineEntry {
    let date: Date
    let count: Int
    let configuration: ConfigurationAppIntent
}

struct TestEntryView : View {
    var entry: Provider.Entry

    var body: some View {
        VStack {
            Text("Counter")
                .font(.headline)
                .foregroundColor(.secondary)

            Text("\(entry.count)")
                .font(.system(size: 32, weight: .bold, design: .rounded))
                .foregroundColor(.blue)
        }
    }
}

struct Test: Widget {
    let kind: String = "Test"

    var body: some WidgetConfiguration {
        AppIntentConfiguration(kind: kind, intent: ConfigurationAppIntent.self, provider: Provider()) { entry in
            TestEntryView(entry: entry)
                .containerBackground(.fill.tertiary, for: .widget)
        }
    }
}

extension ConfigurationAppIntent {
    fileprivate static var smiley: ConfigurationAppIntent {
        let intent = ConfigurationAppIntent()
        intent.favoriteEmoji = "😀"
        return intent
    }

    fileprivate static var starEyes: ConfigurationAppIntent {
        let intent = ConfigurationAppIntent()
        intent.favoriteEmoji = "🤩"
        return intent
    }
}

#Preview(as: .systemSmall) {
    Test()
} timeline: {
    let currentCount = CounterManager.shared.getCurrentCount()
    SimpleEntry(date: .now, count: currentCount, configuration: .smiley)
    SimpleEntry(date: .now, count: currentCount + 1, configuration: .starEyes)
}
