//
//  CounterManager.swift
//  testing-widget-gallery
//
//  Created by gondo on 16/09/2025.
//

import Foundation
import WidgetKit
import Combine

class CounterManager: ObservableObject {
    static let shared = CounterManager()

    // App Group identifier - you'll need to configure this in your project settings
    private let appGroupIdentifier = "group.ios26-test.testing-widget-gallery.shared"
    private let counterKey = "shared_counter"

    @Published var count: Int {
        didSet {
            saveCount()
            // Reload widgets when count changes
            WidgetCenter.shared.reloadAllTimelines()
        }
    }

    private init() {
        // Initialize count first, then load the saved value
        self.count = 0
        self.count = loadCount()
    }
    
    private func loadCount() -> Int {
        guard let userDefaults = UserDefaults(suiteName: appGroupIdentifier) else {
            print("❌ CounterManager: Failed to create UserDefaults with app group: \(appGroupIdentifier)")
            return 0
        }
        let loadedCount = userDefaults.integer(forKey: counterKey)
        print("📖 CounterManager: Loaded count: \(loadedCount)")
        return loadedCount
    }

    private func saveCount() {
        guard let userDefaults = UserDefaults(suiteName: appGroupIdentifier) else {
            print("❌ CounterManager: Failed to create UserDefaults with app group: \(appGroupIdentifier)")
            return
        }
        userDefaults.set(count, forKey: counterKey)
        userDefaults.synchronize() // Force immediate sync
        print("💾 CounterManager: Saved count: \(count)")
    }
    
    func increment() {
        count += 1
    }
    
    func getCurrentCount() -> Int {
        return loadCount()
    }
}
